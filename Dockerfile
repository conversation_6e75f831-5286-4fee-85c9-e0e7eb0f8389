FROM registry.finema.co/finema/idin-core:1.4.5

ARG APP_RELEASE_VERSION
ARG APP_RELEASE_ENVIRONMENT

ENV APP_RELEASE_VERSION=$APP_RELEASE_VERSION
ENV APP_RELEASE_ENVIRONMENT=$APP_RELEASE_ENVIRONMENT

ADD go.mod go.sum /app/
RUN go mod download
ADD . /app/

RUN go build -o main

FROM alpine:3.22.1

ARG APP_RELEASE_VERSION
ARG APP_RELEASE_ENVIRONMENT

ENV APP_RELEASE_VERSION=$APP_RELEASE_VERSION
ENV APP_RELEASE_ENVIRONMENT=$APP_RELEASE_ENVIRONMENT

COPY --from=0 /app/main /main
CMD ./main
