package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm/clause"
)

var Checkin = repository.Make[models.Checkin]()

func CheckinOrderBy(pageOptions *core.PageOptions) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CheckinWithUser(id *string) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if id == nil {
			return
		}
		c.Where("user_id = ?", id)
	}
}

func CheckinWithDateRange(startDate *string, endDate *string) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if startDate != nil && endDate != nil {
			c.Where("DATE(date) BETWEEN ? AND ?", startDate, endDate)
		} else if startDate != nil {
			c.Where("DATE(date) >= ?", startDate)
		} else if endDate != nil {
			c.Where("DATE(date) <= ?", endDate)
		}
	}
}

func CheckinWithTeamCode(teamCodes []string) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if len(teamCodes) == 0 {
			return
		}

		c.Joins("JOIN users ON users.id = checkins.user_id").Where("users.team_code IN ?", teamCodes)
	}
}

func CheckinWithType(checkinType *string) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if checkinType == nil {
			return
		}
		c.Where("type = ?", checkinType)
	}
}

func CheckinWithLeaveType(checkinLeaveType *string) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if checkinLeaveType == nil {
			return
		}
		c.Where("leave_type = ?", checkinLeaveType)
	}
}

func CheckinWithAllRelation() repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		c.Preload(clause.Associations).Preload("User.Team")
	}
}

func CheckinWithIsUnused(isUnused *bool) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if isUnused == nil {
			return
		}
		c.Where("is_unused = ?", isUnused)
	}
}
func CheckinWithSearch(q string) repository.Option[models.Checkin] {
	return func(c repository.IRepository[models.Checkin]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Joins("JOIN users ON users.id = checkins.user_id").
			Where("users.display_name ILIKE ?", searchTerm)
	}
}
