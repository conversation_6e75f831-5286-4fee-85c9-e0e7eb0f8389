package home

import (
	"errors"
	"net/http"

	"github.com/labstack/echo/v4"
	core "gitlab.finema.co/finema/idin-core"
)

func NewHomeHTTP(e *echo.Echo) {
	e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
		return c.JSON(http.StatusOK, core.Map{
			"status": "i'm ok",
		})
	}))
	e.GET("/error", core.WithHTTPContext(func(c core.IHTTPContext) error {
		c.<PERSON>rror(errors.New("test error"), core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "TEST_ERROR",
			Message: "Test error",
		})
		return c.JSON(http.StatusInternalServerError, core.Map{
			"status": "i'm not ok",
		})
	}))
}
